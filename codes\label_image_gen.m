close all 
%Label Name : crops -> i.e.: gTruth.LabelData.crops
lineWidth = 6; %Width of crop row drawing
rotAngle = 45;
thresh = 0.4;
lineColor = 'w'; %Colour for crop row drawing
format = '.jpg';
Gpath = 'Dataset/GT/';%Path to save Ground Truth Images
Ipath = 'Dataset/Crops/';%Path to save resized RGB Images

load('labels.mat');%Ground Truth data from Matlab ImageLabeller

%%%%%%%%%%%%%%%% Fix missing paths if needed %%%%%%%%%%%%%%%%%%
%currentPathDataSource = convertCharsToStrings(gTruth.DataSource);
%newPathDataSource = "Your\Path\Here"
%alternativePaths = {[currentPathDataSource newPathDataSource]};
%unresolvedPaths = changeFilePaths(gTruth,alternativePaths)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

Inum = size(gTruth.LabelData,1); %Number of images in ground truth data
%count=0; %Initialize Count Manually in CMD line. Or Uncomment this if you
%want sequential naming

for i=1:Inum   %Read into each image
    I = imread(gTruth.DataSource.Source{i,1});
    O = zeros(size(I));%Initialize output image
    figure
    imshow(O,'Border','tight');
    hold on
    for j=1:size(gTruth.LabelData.crop{i,1},1)%Read into each line label
        for k=2:size(gTruth.LabelData.crop{i,1}{j,1},1)%Read into each point on line label
            x1 = gTruth.LabelData.crop{i,1}{j,1}(k-1,1);
            y1 = gTruth.LabelData.crop{i,1}{j,1}(k-1,2);
            x2 = gTruth.LabelData.crop{i,1}{j,1}(k,1);
            y2 = gTruth.LabelData.crop{i,1}{j,1}(k,2);
            plot([x1,x2],[y1,y2],'Color',lineColor,'lineWidth',lineWidth);
        end
    end
    hold off
    nameG = strcat(Gpath,int2str(i),format);
    nameI = strcat(Ipath,int2str(i),format);
    set(gcf,'color','w');
    saveas(gcf,'temp.jpg','jpeg');
    I2 = imread('temp.jpg');
    %I4 = imresize(I2, [725 NaN]);%For rotated GT
    I2 = imresize(I2, [512 NaN]);%For Ground Truth
    %I3 = imresize(I, [725 NaN]);%For rotated crop Image
    I = imresize(I, [512 NaN]);
    
%     nameI = strcat(Ipath,int2str(count),format);%CCW Cropped Image
%     O = imrotate(I3,rotAngle,'bilinear');
%     O = imcrop(O,[457 457 511 511]);
%     imwrite(O,nameI);
%     
%     nameG = strcat(Gpath,int2str(count),format);%CCW Cropped GT
%     O = imrotate(I4,rotAngle,'bilinear');
%     O = imcrop(O,[457 457 511 511]);
%     O = im2bw(O,thresh);
%     imwrite(O,nameG);
%     
%     count = count+1;
%     
%     nameI = strcat(Ipath,int2str(count),format);%CW Cropped Image
%     O = imrotate(I3,-rotAngle,'bilinear');
%     O = imcrop(O,[475 473 511 511]);
%     imwrite(O,nameI);
%     
%     nameG = strcat(Gpath,int2str(count),format);%CW Cropped GT
%     O = imrotate(I4,-rotAngle,'bilinear');
%     O = imcrop(O,[475 473 511 511]);
%     O = im2bw(O,thresh);
%     imwrite(O,nameG);
%     
%     count = count+1;
    
%     nameI = strcat(Ipath,int2str(count),format);%Left Cropped Image
%     O = imcrop(I,[0 0 512 512]);
%     imwrite(O,nameI);
%     
%     nameG = strcat(Gpath,int2str(count),format);%Left Cropped GT
%     O = imcrop(I2,[0 0 512 512]);
%     O = im2bw(O,thresh);
%     imwrite(O,nameG);
%     
%     count = count+1;
%     sz = size(I);
%     xmin = sz(1,2)-512;
%     
%     nameI = strcat(Ipath,int2str(count),format);%Right Cropped Image
%     O = imcrop(I,[xmin 0 511 512]);
%     imwrite(O,nameI);
%     
%     nameG = strcat(Gpath,int2str(count),format);%Right Cropped GT
%     O = imcrop(I2,[xmin 0 511 512]);
%     O = im2bw(O,thresh);
%     imwrite(O,nameG);
%     
%     count = count+1;  
    
    nameI = strcat(Ipath,int2str(count),format);%Resized Image
    O = imresize(I, [512 512]);
    imwrite(O,nameI);
    
    nameG = strcat(Gpath,int2str(count),format);%Resized GT
    O = imresize(I2, [512 512]);
    O = im2bw(O,thresh);
    imwrite(O,nameG);
    
    count = count+1;
    close all
end