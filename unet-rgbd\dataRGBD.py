# -*- coding:utf-8 -*-

from keras.preprocessing.image import img_to_array, load_img
import numpy as np
import glob


class dataProcess(object):
    def __init__(self, out_rows, out_cols, data_path="./data/train/image", depth_path="./data/train/depth", label_path="./data/train/label",
                 test_path="./data/test/image", testdepth_path="./data/test/depth", testlabel_path="./data/test/label", npy_path="./npydata", img_type="jpg"):
        self.out_rows = out_rows
        self.out_cols = out_cols
        self.data_path = data_path
        self.depth_path = depth_path
        self.label_path = label_path
        self.img_type = img_type
        self.test_path = test_path
        self.testdepth_path = testdepth_path
        self.testlabel_path = testlabel_path
        self.npy_path = npy_path

    def create_train_data(self):
        i = 0
        print('Creating training images...')
        imgs = glob.glob(self.data_path+"/*."+self.img_type)
        depths = glob.glob(self.depth_path+"/*."+self.img_type)
        imgdatas = np.ndarray((len(imgs), self.out_rows, self.out_cols, 4), dtype=np.uint8)
        imglabels = np.ndarray((len(imgs), self.out_rows, self.out_cols, 1), dtype=np.uint8)

        for x in range(len(imgs)):
            imgpath = imgs[x]
            pic_name = imgpath.split('/')[-1]
            labelpath = self.label_path + '/' + pic_name
            depthpath = self.depth_path + '/' + pic_name
            img = load_img(imgpath, color_mode='rgb', target_size=[512, 512])
            depth = load_img(depthpath, color_mode='grayscale', target_size=[512, 512])
            label = load_img(labelpath, color_mode='grayscale', target_size=[512, 512])
            img = img_to_array(img)
            depth = img_to_array(depth)
            img = np.dstack((img, depth))#rgbd array
            label = img_to_array(label)
            imgdatas[i] = img
            imglabels[i] = label
            if i % 100 == 0:
                print('Done: {0}/{1} images'.format(i, len(imgs)))
            i += 1
        
        print('loading done')
        np.save(self.npy_path + '/imgs_train.npy', imgdatas)
        np.save(self.npy_path + '/imgs_mask_train.npy', imglabels)
        print('Saving to .npy files done.')

    def create_test_all(self):
        i = 0
        print('Creating testall images...')
        imgs = glob.glob(self.test_path+"/*."+self.img_type)
        depths = glob.glob(self.depth_path+"/*."+self.img_type)
        imgdatas = np.ndarray((len(imgs), self.out_rows, self.out_cols, 4), dtype=np.uint8)
        imglabels = np.ndarray((len(imgs), self.out_rows, self.out_cols, 1), dtype=np.uint8)

        for x in range(len(imgs)):
            imgpath = imgs[x]
            pic_name = imgpath.split('/')[-1]
            labelpath = self.testlabel_path + '/' + pic_name
            depthpath = self.testdepth_path + '/' + pic_name
            img = load_img(imgpath, color_mode='rgb', target_size=[512, 512])
            depth = load_img(depthpath, color_mode='grayscale', target_size=[512, 512])
            label = load_img(labelpath, color_mode='grayscale', target_size=[512, 512])
            img = img_to_array(img)
            depth = img_to_array(depth)
            img = np.dstack((img, depth))#rgbd array
            label = img_to_array(label)
            imgdatas[i] = img
            imglabels[i] = label
            if i % 100 == 0:
                print('Done: {0}/{1} images'.format(i, len(imgs)))
            i += 1

        print('loading done')
        np.save(self.npy_path + '/imgs_test.npy', imgdatas)
        np.save(self.npy_path + '/imgs_mask_test.npy', imglabels)
        print('Saving to .npy files done.')

    def create_test_data(self):
        i = 0
        print('Creating test images...')
        imgs = glob.glob(self.test_path + "/*." + self.img_type)
        imgdatas = np.ndarray((len(imgs), self.out_rows, self.out_cols, 3), dtype=np.uint8)
        testpathlist = []

        for imgname in imgs:
            testpath = imgname
            testpathlist.append(testpath)
            img = load_img(testpath, color_mode='rgb', target_size=[512, 512])
            img = img_to_array(img)
            imgdatas[i] = img
            i += 1

        txtname = './results/pic.txt'
        with open(txtname, 'w') as f:
            for i in range(len(testpathlist)):
                f.writelines(testpathlist[i] + '\n')
        print('loading done')
        np.save(self.npy_path + '/imgs_test.npy', imgdatas)
        print('Saving to imgs_test.npy files done.')

    def load_train_data(self):
        print('load train images...')
        imgs_train = np.load(self.npy_path + "/imgs_train.npy")
        imgs_mask_train = np.load(self.npy_path + "/imgs_mask_train.npy")
        imgs_train = imgs_train.astype('float32')
        imgs_mask_train = imgs_mask_train.astype('float32')
        imgs_train /= 255
        imgs_mask_train /= 255
        imgs_mask_train[imgs_mask_train > 0.5] = 1  # 白
        imgs_mask_train[imgs_mask_train <= 0.5] = 0  # 黑
        return imgs_train, imgs_mask_train

    def load_test_data(self):
        print('-' * 30)
        print('load test images...')
        print('-' * 30)
        imgs_test = np.load(self.npy_path + "/imgs_test.npy")
        imgs_test = imgs_test.astype('float32')
        imgs_test /= 255
        return imgs_test

    def load_test_labels(self):
        print('-' * 30)
        print('load test label images...')
        print('-' * 30)
        imgs_testlabels = np.load(self.npy_path + "/imgs_mask_test.npy")
        imgs_testlabels = imgs_testlabels.astype('float32')
        imgs_testlabels /= 255
        return imgs_testlabels



if __name__ == "__main__":
    mydata = dataProcess(512, 512)
    mydata.create_train_data()
    mydata.create_test_all()
    #mydata.create_test_data()
