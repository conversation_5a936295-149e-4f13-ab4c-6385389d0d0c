import os
import cv2
import numpy as np
from glob import glob
import json

def extract_line_points_from_mask(mask_path, min_line_length=50):
    """
    从掩码图中提取线条的起点和终点

    参数:
        mask_path: 掩码图像路径
        min_line_length: 最小线段长度阈值，短于此长度的线段将被过滤掉

    返回:
        线条的起点和终点列表 [(start_point, end_point), ...]
    """
    # 读取掩码图
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    if mask is None:
        print(f"无法读取掩码图: {mask_path}")
        return []
    
    # 二值化掩码图（如果需要）
    _, binary_mask = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
    
    # 查找连通区域
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_mask, connectivity=8)
    
    # 提取每个连通区域的线条信息
    line_points = []
    
    # 跳过背景（标签0）
    for i in range(1, num_labels):
        # 创建当前连通区域的掩码
        component_mask = np.zeros_like(binary_mask)
        component_mask[labels == i] = 255
        
        # 查找非零点
        points = np.column_stack(np.where(component_mask > 0))
        if len(points) < 2:
            continue
        
        # 使用PCA找到主方向
        mean = np.mean(points, axis=0)
        points_centered = points - mean
        cov = np.cov(points_centered.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov)
        
        # 找到主要方向的特征向量
        idx = np.argmax(eigenvalues)
        direction = eigenvectors[:, idx]
        
        # 计算线条的起点和终点
        # 将点投影到主方向上
        projections = np.dot(points_centered, direction)
        min_idx = np.argmin(projections)
        max_idx = np.argmax(projections)
        
        # 获取起点和终点（注意：y在前，x在后）
        start_point = (int(points[min_idx][1]), int(points[min_idx][0]))  # (x, y)
        end_point = (int(points[max_idx][1]), int(points[max_idx][0]))    # (x, y)

        # 计算线段长度
        line_length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)

        # 只保留长度大于等于阈值的线段
        if line_length >= min_line_length:
            line_points.append((start_point, end_point))
    
    return line_points

def save_line_points(line_points, output_file):
    """
    保存线条点到JSON文件
    
    参数:
        line_points: 线条点列表 [(start_point, end_point), ...]
        output_file: 输出文件路径
    """
    # 将点转换为可序列化的格式
    serializable_points = []
    for start_point, end_point in line_points:
        serializable_points.append({
            "start_point": list(start_point),
            "end_point": list(end_point)
        })
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 保存到JSON文件
    with open(output_file, 'w') as f:
        json.dump(serializable_points, f, indent=2)
    
    print(f"线条点已保存到: {output_file}")

def load_line_points(input_file):
    """
    从JSON文件加载线条点
    
    参数:
        input_file: 输入文件路径
        
    返回:
        线条点列表 [(start_point, end_point), ...]
    """
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return []
    
    try:
        with open(input_file, 'r') as f:
            data = json.load(f)
        
        line_points = []
        for item in data:
            start_point = tuple(item["start_point"])
            end_point = tuple(item["end_point"])
            line_points.append((start_point, end_point))
        
        return line_points
    except Exception as e:
        print(f"加载线条点时出错: {e}")
        return []

def draw_lines_on_image(image_path, line_points=None, mask_path=None, output_path=None, colors=None, line_thickness=3, min_line_length=50):
    """
    在原图上绘制线条
    
    参数:
        image_path: 原始图像路径
        line_points: 线条点列表 [(start_point, end_point), ...]，如果为None则从mask_path提取
        mask_path: 掩码图像路径，如果line_points为None则从此提取线条
        output_path: 输出图像路径，如果为None则自动生成
        colors: 线条颜色列表，BGR格式
        line_thickness: 线条粗细
        min_line_length: 最小线段长度阈值，短于此长度的线段将被过滤掉
    """
    # 读取原图
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    # 如果没有提供线条点，从掩码图提取
    if line_points is None and mask_path is not None:
        line_points = extract_line_points_from_mask(mask_path, min_line_length)
    
    if not line_points:
        print("未提供线条点，且无法从掩码图提取")
        return None
    
    # 如果没有提供输出路径，自动生成
    if output_path is None:
        output_path = f"{os.path.splitext(image_path)[0]}_lines.jpg"
    
    # 如果没有提供颜色，使用默认颜色
    if colors is None:
        colors = [(0, 255, 0), (0, 0, 255), (255, 0, 0), (0, 255, 255), (255, 0, 255)]
    
    # 创建输出图像的副本
    result = image.copy()
    
    # 绘制线条
    for i, (start_point, end_point) in enumerate(line_points):
        # 计算线段长度
        line_length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)

        # 只绘制长度大于等于阈值的线段
        if line_length >= min_line_length:
            color = colors[i % len(colors)]
            cv2.line(result, start_point, end_point, color, line_thickness)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果
    cv2.imwrite(output_path, result)
    print(f"处理完成: {os.path.basename(image_path)} -> {os.path.basename(output_path)}")
    
    return result

def process_single_image(image_path, mask_path, points_file=None, output_path=None, line_thickness=3, min_line_length=50):
    """
    处理单张图像
    
    参数:
        image_path: 原始图像路径
        mask_path: 掩码图像路径
        points_file: 保存/加载线条点的文件路径，如果为None则不保存
        output_path: 输出图像路径，如果为None则自动生成
        line_thickness: 线条粗细
        min_line_length: 最小线段长度阈值，短于此长度的线段将被过滤掉
    """
    # 如果没有提供输出路径，自动生成
    if output_path is None:
        output_path = f"{os.path.splitext(image_path)[0]}_lines.jpg"
    
    # 如果没有提供点文件路径，自动生成
    if points_file is None and mask_path is not None:
        points_file = f"{os.path.splitext(mask_path)[0]}_points.json"
    
    # 定义颜色：绿色和红色
    colors = [(0, 255, 0), (0, 0, 255), (255, 0, 0), (0, 255, 255), (255, 0, 255)]
    
    # 尝试加载已保存的线条点
    line_points = []
    if points_file and os.path.exists(points_file):
        print(f"从文件加载线条点: {points_file}")
        line_points = load_line_points(points_file)
    
    # 如果没有加载到线条点，从掩码图提取
    if not line_points and mask_path:
        print(f"从掩码图提取线条点: {mask_path}")
        line_points = extract_line_points_from_mask(mask_path, min_line_length)
        
        # 保存提取的线条点
        if points_file and line_points:
            save_line_points(line_points, points_file)
    
    # 在原图上绘制线条
    return draw_lines_on_image(image_path, line_points, None, output_path, colors, line_thickness, min_line_length)

def process_all_images(image_dir, mask_dir, output_dir, points_dir=None, line_thickness=3, min_line_length=50):
    """
    处理目录中的所有图像
    
    参数:
        image_dir: 原始图像目录
        mask_dir: 掩码图像目录
        output_dir: 输出图像目录
        points_dir: 线条点文件目录，如果为None则不保存
        line_thickness: 线条粗细
        min_line_length: 最小线段长度阈值，短于此长度的线段将被过滤掉
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    if points_dir:
        os.makedirs(points_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_files = glob(os.path.join(image_dir, "*.jpg"))
    print(f"找到 {len(image_files)} 个图像文件")
    
    for image_path in image_files:
        # 获取文件名
        filename = os.path.basename(image_path)
        
        # 构建对应的掩码路径
        mask_path = os.path.join(mask_dir, filename)
        
        # 如果掩码不存在，尝试查找对应的结果掩码
        if not os.path.exists(mask_path):
            mask_path = os.path.join(mask_dir, filename.replace('.jpg', '.jpg'))
        
        # 构建输出路径
        output_path = os.path.join(output_dir, f"{os.path.splitext(filename)[0]}_lines.jpg")
        
        # 构建点文件路径
        points_file = None
        if points_dir:
            points_file = os.path.join(points_dir, f"{os.path.splitext(filename)[0]}_points.json")
        
        # 如果掩码存在，处理图像
        if os.path.exists(mask_path):
            process_single_image(image_path, mask_path, points_file, output_path, line_thickness, min_line_length)
        else:
            print(f"未找到对应掩码: {filename}")

def visualize_mask_extraction(mask_path, output_path=None, min_line_length=50):
    """
    可视化从掩码中提取的线条
    
    参数:
        mask_path: 掩码图像路径
        output_path: 输出图像路径，如果为None则自动生成
        min_line_length: 最小线段长度阈值，短于此长度的线段将被过滤掉
    """
    if output_path is None:
        output_path = f"{os.path.splitext(mask_path)[0]}_extraction.jpg"
    
    print(mask_path)
    # 读取掩码图
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    if mask is None:
        print(f"无法读取掩码图: {mask_path}")
        return None
    
    # 转换为彩色图像以便绘制彩色线条
    vis_image = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
    
    # 提取线条点
    line_points = extract_line_points_from_mask(mask_path, min_line_length)
    if not line_points:
        print(f"未能从掩码图提取到线条: {mask_path}")
        return None
    
    # 定义颜色
    colors = [(0, 255, 0), (0, 0, 255), (255, 0, 0), (0, 255, 255), (255, 0, 255)]
    
    # 绘制线条
    line_thickness = 2
    for i, (start_point, end_point) in enumerate(line_points):
        color = colors[i % len(colors)]
        cv2.line(vis_image, start_point, end_point, color, line_thickness)
        
        # 在起点和终点绘制圆点
        cv2.circle(vis_image, start_point, 5, color, -1)
        cv2.circle(vis_image, end_point, 5, (255, 255, 255), -1)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果
    cv2.imwrite(output_path, vis_image)
    print(f"提取可视化完成: {os.path.basename(mask_path)} -> {os.path.basename(output_path)}")
    
    return vis_image

if __name__ == "__main__":
    # 设置路径
    image_path = "../data/image/"
    mask_path = r"C:/Users/<USER>/Desktop/res/"
    output_path = r"inference/overlay/"
    points_file = r"inference/points/"

    # 设置线条粗细和最小长度
    line_thickness = 5  # 可以根据需要调整线条粗细
    min_line_length = 30  # 最小线段长度，可以根据需要调整
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    os.makedirs(os.path.dirname(points_file), exist_ok=True)
    
    # 可视化掩码提取过程
    vis_output_path = r"inference/overlay/IMG_20250610_193225_13_extraction.jpg"
    visualize_mask_extraction(mask_path, vis_output_path, min_line_length)
    
    # 处理单张图像
    process_single_image(image_path, mask_path, points_file, output_path, line_thickness, min_line_length)
    
    print("处理完成!")